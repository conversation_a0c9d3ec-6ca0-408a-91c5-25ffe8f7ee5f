<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:App/etc/routes.xsd">
    <router id="standard">
        <route id="mpsellercategory" frontName="mpsellercategory">
            <!-- Override entire route with single catch-all controller -->
            <module name="Comave_SellerCategoryRestriction" before="Webkul_MpSellerCategory"/>
        </route>
    </router>
</config>
