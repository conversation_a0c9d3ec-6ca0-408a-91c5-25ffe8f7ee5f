<?php

declare(strict_types=1);

namespace Comave\SellerCategoryRestriction\Controller\Index;

use Magento\Framework\App\ActionInterface;
use Magento\Framework\Controller\Result\Forward;
use Magento\Framework\Controller\ResultFactory;

/**
 * Catch-all controller for ALL mpsellercategory requests
 * This handles any URL like /mpsellercategory/* and returns 404
 */
class Index implements ActionInterface
{
    public function __construct(
        private readonly ResultFactory $resultFactory
    ) {}

    public function execute(): Forward
    {
        // Return 404 for all seller category management requests
        $result = $this->resultFactory->create(ResultFactory::TYPE_FORWARD);
        $result->forward('noroute');
        
        return $result;
    }
}
